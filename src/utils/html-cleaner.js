import sanitizeHtml from "sanitize-html";

class HTMLCleaner {
  static defaultOptions = {
    allowedTags: [
      "p",
      "br",
      "strong",
      "em",
      "b",
      "i",
      "u",
      "strike",
      "h1",
      "h2",
      "h3",
      "h4",
      "h5",
      "h6",
      "ul",
      "ol",
      "li",
      "blockquote",
      "pre",
      "code",
      "a",
      "img",
      "div",
      "span",
      "table",
      "thead",
      "tbody",
      "tr",
      "th",
      "td",
    ],
    allowedAttributes: {
      a: ["href", "title", "target"],
      img: ["src", "alt", "title", "width", "height"],
      "*": ["class", "id"],
    },
    allowedSchemes: ["http", "https", "mailto", "tel"],
    allowedSchemesByTag: {
      img: ["http", "https", "data"],
    },
    transformTags: {
      a: function (tagName, attribs) {
        return {
          tagName: "a",
          attribs: {
            ...attribs,
            rel: "noopener noreferrer",
            target: attribs.target || "_blank",
          },
        };
      },
    },
    exclusiveFilter: function (frame) {
      const emptyTags = ["div", "span", "p"];
      return emptyTags.includes(frame.tag) && !frame.text.trim();
    },
  };

  static strictOptions = {
    allowedTags: [
      "p",
      "br",
      "strong",
      "em",
      "h1",
      "h2",
      "h3",
      "ul",
      "ol",
      "li",
      "a",
    ],
    allowedAttributes: {
      a: ["href", "title"],
    },
    allowedSchemes: ["http", "https"],
    transformTags: {
      a: function (tagName, attribs) {
        return {
          tagName: "a",
          attribs: {
            href: attribs.href,
            title: attribs.title,
            rel: "noopener noreferrer nofollow",
            target: "_blank",
          },
        };
      },
    },
  };

  static textOnlyOptions = {
    allowedTags: [],
    allowedAttributes: {},
    textFilter: function (text) {
      return text.replace(/\s+/g, " ").trim();
    },
  };

  static clean(html, customOptions = {}) {
    if (!html || typeof html !== "string") {
      return "";
    }

    const options = { ...this.defaultOptions, ...customOptions };
    const cleaned = sanitizeHtml(html, options);

    return this.postProcess(cleaned);
  }

  static cleanStrict(html, customOptions = {}) {
    if (!html || typeof html !== "string") {
      return "";
    }

    const options = { ...this.strictOptions, ...customOptions };
    return sanitizeHtml(html, options);
  }

  static extractText(html) {
    if (!html || typeof html !== "string") {
      return "";
    }

    return sanitizeHtml(html, this.textOnlyOptions);
  }

  static cleanWithCheerio($) {
    $(
      "script, style, noscript, iframe, object, embed, applet, form, input, textarea, select, button",
    ).remove();

    $("*")
      .contents()
      .filter(function () {
        return this.type === "comment";
      })
      .remove();

    $('[class*="ad"], [class*="advertisement"], [id*="ad"]').remove();
    $('[class*="tracking"], [class*="analytics"], [class*="gtm"]').remove();
    $('[class*="social"], [class*="share"]').remove();

    $("*").each(function () {
      const element = $(this);
      const attribs = element.get(0).attribs || {};

      Object.keys(attribs).forEach((attr) => {
        if (attr.startsWith("on")) {
          element.removeAttr(attr);
        }

        if (
          attr.startsWith("data-") &&
          !["data-src", "data-alt"].includes(attr)
        ) {
          element.removeAttr(attr);
        }

        if (
          attribs[attr] &&
          attribs[attr].toLowerCase().includes("javascript:")
        ) {
          element.removeAttr(attr);
        }
      });
    });

    $("*").each(function () {
      const text = $(this).text();
      if (text && text.trim()) {
        $(this).text(text.replace(/\s+/g, " ").trim());
      }
    });

    return $.html();
  }

  static postProcess(html) {
    return html
      .replace(/\s+/g, " ")
      .replace(/<p>\s*<\/p>/g, "")
      .replace(/(<br\s*\/?>){3,}/g, "<br><br>")
      .replace(/>\s+</g, "><")
      .trim();
  }

  static validate(html) {
    if (!html || typeof html !== "string") {
      return false;
    }

    if (/<script/i.test(html)) {
      return false;
    }

    if (/on\w+\s*=/i.test(html)) {
      return false;
    }

    if (/javascript:/i.test(html)) {
      return false;
    }

    if (html.length < 50) {
      return false;
    }

    return true;
  }

  static extractMainContent($) {
    const contentSelectors = [
      "article",
      "main",
      ".content",
      "#content",
      ".post-content",
      ".entry-content",
      ".article-content",
      ".story-body",
      '[role="main"]',
    ];

    for (const selector of contentSelectors) {
      const element = $(selector);
      if (element.length > 0 && element.text().trim().length > 100) {
        return element.html();
      }
    }

    $("header, nav, footer, aside, .sidebar, .navigation, .menu").remove();
    return $("body").html() || "";
  }
}

export default HTMLCleaner;
