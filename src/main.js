import { fork } from "node:child_process";

import {
  Configuration,
  Dataset,
  AdaptivePlaywrightCrawler,
  log,
} from "crawlee";

import {
  BrowserName,
  DeviceCategory,
  OperatingSystemsName,
} from "@crawlee/browser-pool";

import { launchOptions } from 'camoufox-js';
import { firefox } from 'playwright';

import { router } from "./routes/index.js";
import { getOrInitQueue } from "./shared.js";
import { sites } from "./sites/index.js";

const initSites = async () => {
  const requestQueue = await getOrInitQueue(true);

  for (const site of sites) {
    if (!site.enabled) continue;
    await requestQueue.addRequest({
      url: `${site.baseUrl}${site.startPath}`,
      userData: { site },
    });
  }

  //await crawler.addRequests(pageUrls);
};

async function main() {
  if (!process.env.IN_WORKER_THREAD) {
    log.info("Setting up worker threads.");

    // Initialize sites in the main process before spawning workers
    log.info("Initializing sites and populating shared queue...");
    await initSites();
    log.info("✅ Sites initialized and queue populated");

    const currentFile = new URL(import.meta.url).pathname;

    const promises = [];

    for (let i = 0; i < 2; i++) {
      // Small delay between worker spawning to ensure proper queue initialization
      if (i > 0) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      const proc = fork(currentFile, {
        env: {
          ...process.env,
          IN_WORKER_THREAD: "true",
          WORKER_INDEX: String(i),
        },
      });

      proc.on("online", () => {
        log.info(`Process ${i} is online.`);

        proc.stdout.on("data", (data) => {
          console.log(data.toString());
        });

        proc.stderr.on("data", (data) => {
          console.error(data.toString());
        });
      });

      proc.on("message", async (data) => {
        log.debug(`Process ${i} sent data.`, data);
        await Dataset.pushData(data);
      });

      promises.push(
        new Promise((resolve) => {
          proc.once("exit", (code, signal) => {
            log.info(
              `Process ${i} exited with code ${code} and signal ${signal}`
            );
            resolve();
          });
        })
      );
    }

    await Promise.all(promises);

    log.info("Crawling complete!");
  } else {
  const workerLogger = log.child({
    prefix: `[Worker ${process.env.WORKER_INDEX}]`,
  });

  //workerLogger.setLevel(log.LEVELS.DEBUG);

  Configuration.set("purgeOnStart", false);

  const config = new Configuration({
    storageClientOptions: {
      localDataDirectory: `./storage/shared`, // Shared storage for request queue
    },
  });

  workerLogger.debug("Setting up crawler with shared queue...");
  const sharedQueue = await getOrInitQueue(false);
  workerLogger.info(`🔗 Worker ${process.env.WORKER_INDEX} connected to shared queue`);

  const crawler = new AdaptivePlaywrightCrawler(
    {
      browserPoolOptions: {
        useFingerprints: false,
        fingerprintOptions: {
          fingerprintGeneratorOptions: {
            browsers: [
              {
                name: BrowserName.edge,
                minVersion: 96,
              },
            ],
            devices: [DeviceCategory.desktop],
            operatingSystems: [OperatingSystemsName.windows],
          },
        },
      },
      postNavigationHooks: [
        async ({ handleCloudflareChallenge }) => {
          await handleCloudflareChallenge();
        },
      ],
      launchContext: {
        launcher: firefox,
        launchOptions: await launchOptions({
          headless: true,
        }),
      },
      renderingTypeDetectionRatio: 0.1,
      log: workerLogger,
      requestHandler: router,
      experiments: {
        requestLocking: true,
      },
      requestQueue: sharedQueue,
      maxConcurrency: 5,
    },
    config
  );

  // Don't initialize sites in worker - already done by main process
  // await initSites();

  // Log queue status before starting
  const queueInfo = await sharedQueue.getInfo();
  workerLogger.info(`📊 Worker ${process.env.WORKER_INDEX} starting with queue status:`, {
    totalRequestCount: queueInfo.totalRequestCount,
    handledRequestCount: queueInfo.handledRequestCount,
    pendingRequestCount: queueInfo.pendingRequestCount
  });

  await crawler.run();

    // Log final queue status
    const finalQueueInfo = await sharedQueue.getInfo();
    workerLogger.info(`🏁 Worker ${process.env.WORKER_INDEX} finished. Final queue status:`, {
      totalRequestCount: finalQueueInfo.totalRequestCount,
      handledRequestCount: finalQueueInfo.handledRequestCount,
      pendingRequestCount: finalQueueInfo.pendingRequestCount
    });
  }
}

// Run the main function
main().catch(console.error);
