export const mangakakalove = {
  name: "mangakakalove",
  locale: "en",
  enabled: false,
  baseUrl: "https://mangakakalove.com",
  startPath: "/manga-list/latest-manga",
  maxPages: 5,
  selectors: {
    listManga: {
      container: ".manga-list",
      mangaLink: ".list-truyen-item-wrap h3 a",
      nextPage: ".page_select + a",
      lastPage: ".page_last",
    },
    detailManga: {
      title: ".manga-info-text h1",
      cover: ".manga-info-pic img",
      description: "#contentBox",
      authors: "li:contains(Author(s)) a",
      genres: ".genres a",
      status: "li:contains(Status)",
      chapterLink: ".chapter-list a",
    },
    detailChapter: {
      chapterTitle: ".info-top-chapter h2",
      pages: ".container-chapter-reader img",
    },
  },
};
