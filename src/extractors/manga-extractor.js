import { htmlToText } from "crawlee";
import slugify from "slugify";

export function mangaDetail($, mangaDetailSelectors) {
  try {
    // Validate inputs
    if (!$ || !mangaDetailSelectors) {
      console.warn('Invalid parameters passed to mangaDetail extractor');
      return {};
    }

    const title = $(mangaDetailSelectors.title).text().trim();
    const slug = slugify(title, { lower: true });

    // Safely extract description HTML and convert to text
    const descriptionElement = $(mangaDetailSelectors.description);
    const descriptionHtml = descriptionElement.length > 0 ? descriptionElement.html() : '';
    const description = descriptionHtml ? htmlToText(descriptionHtml) : '';

    const detail = {
      title,
      slug,
      description,
      coverImage: $(mangaDetailSelectors.cover).attr("src") || "",
      status: $(mangaDetailSelectors.status).text().trim() || "",
      authors: $(mangaDetailSelectors.authors)
        .map((_, author) => $(author).text().trim())
        .get(),
      genres: $(mangaDetailSelectors.genres)
        .map((_, genre) => $(genre).text().trim())
        .get(),
    };

    return detail;
  } catch (error) {
    console.error('Error extracting manga detail:', error);
    return {
      title: '',
      slug: '',
      description: '',
      coverImage: '',
      status: '',
      authors: [],
      genres: [],
    };
  }
}
