import { RequestQueueV2, log } from "crawlee";

// Create the request queue that also supports parallelization
let queue;

/**
 * @param {boolean} makeFresh Whether the queue should be cleared before returning it
 * @returns The queue
 */
export async function getOrInitQueue(makeFresh = false) {
  if (queue) {
    log.debug("🔄 Returning existing shared queue instance");
    return queue;
  }

  log.info("🚀 Opening shared request queue: crawler-urls");
  queue = await RequestQueueV2.open("crawler-urls");

  if (makeFresh) {
    log.info("🧹 Clearing existing queue data");
    await queue.drop();
    queue = await RequestQueueV2.open("crawler-urls");
    log.info("✅ Fresh queue created");
  }

  log.info("✅ Shared queue initialized and ready for multi-worker access");
  return queue;
}
