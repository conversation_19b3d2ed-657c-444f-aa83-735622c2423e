import slugify from "slugify";
import { extractNumberFromString } from "../utils/index.js";

export function chapterDetail($, chapterDetailSelectors, siteConfig) {
  try {
    const chapterTitle = $(chapterDetailSelectors.chapterTitle).text().trim();
    const chapterNumber = extractNumberFromString(chapterTitle, {
      float: true,
    });
    const prefix = siteConfig.locale === "vi" ? "chuong" : "chapter";
    const chapterSlug = slugify(`${prefix}-${chapterNumber}`, { lower: true });
    const chapterDetail = {
      title: chapterTitle,
      chapterNumber: chapterNumber,
      slug: chapterSlug,
      pages: $(chapterDetailSelectors.pages)
        .map((_, page) => $(page).attr("src"))
        .get(),
    };

    return chapterDetail;
  } catch (error) {
    console.error("Error extracting chapter detail:", error);
    return {};
  }
}
