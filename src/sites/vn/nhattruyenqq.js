export const nhattruyenqq = {
  name: "nhattruyenqq",
  locale: "vi",
  enabled: false,
  baseUrl: "https://nhattruyenqq.com",
  startPath: "/",
  listPageUrl: "/truyen-moi-cap-nhat/trang-$page.html",
  maxPages: 5,
  selectors: {
    listManga: {
      container: "div.items",
      mangaLink: "div.item .image a",
      nextPage: "ul.pagination a.page-link[rel='next']",
      lastPage: "",
    },
    detailManga: {
      title: "h1.title-detail",
      cover: "div.col-image img",
      description: "div.detail-content",
      authors: "li.author p.col-xs-8",
      genres: "li.kind p.col-xs-8 a",
      status: "li.status p:last-child",
      chapterLink: "#chapter_list .chapter a",
    },
    detailChapter: {
      chapterTitle: "div.top > h1.txt-primary",
      pages: ".page-chapter img",
      waitForSelector: ".page-chapter img[data-loaded='true']:last-child",
    },
  },
};
