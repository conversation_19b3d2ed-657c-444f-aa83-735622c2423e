import { createAdaptivePlaywrightRouter } from "crawlee";
import { handleMangaList } from "./manga-list-route.js";
import { handleMangaDetail } from "./manga-detail-route.js";
import { handleChapterDetail } from "./chapter-detail-route.js";

export const router = createAdaptivePlaywrightRouter();

router.addDefault<PERSON>and<PERSON>(handleMangaList);

router.addHandler("MANGA_LIST", handleMangaList);

router.addHandler("MANGA_DETAIL", handleMangaDetail);

router.addHandler("CHAPTER_DETAIL", handleChapterDetail);
