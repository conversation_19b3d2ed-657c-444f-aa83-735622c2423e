import { cpus } from "node:os";

/**
 * Production-ready configuration for the manga crawler
 */
export const config = {
  // Worker configuration
  workers: {
    // Default worker count based on CPU cores, max 4 for stability
    defaultCount: Math.min(cpus().length, 4),
    // Maximum concurrency per worker to prevent overload
    maxConcurrencyPerWorker: 3,
    // Worker timeout in milliseconds (5 minutes)
    timeoutMs: 300000,
    // Stats reporting interval in milliseconds (30 seconds)
    statsIntervalMs: 30000,
  },

  // Queue configuration
  queue: {
    // Default queue name
    name: process.env.QUEUE_NAME || "manga-crawler-queue",
    // Retry configuration for queue operations
    retries: 3,
    retryDelayMs: 1000,
  },

  // Crawler configuration
  crawler: {
    // Request timeout in seconds
    requestTimeoutSecs: 60,
    // Navigation timeout in seconds
    navigationTimeoutSecs: 30,
    // Maximum request retries
    maxRetries: 3,
    // Headless mode (can be overridden by HEADLESS env var)
    headless: process.env.HEADLESS !== 'false',
  },

  // Logging configuration
  logging: {
    // Default log level
    level: process.env.LOG_LEVEL || 'INFO',
    // Enable debug mode
    debug: process.env.DEBUG === 'true',
  },

  // Storage configuration
  storage: {
    // Shared storage directory for queue
    sharedDir: './storage/shared',
    // Worker-specific storage pattern
    workerDirPattern: './storage/worker-{workerId}',
  },

  // Performance configuration
  performance: {
    // Enable performance monitoring
    monitoring: process.env.ENABLE_MONITORING === 'true',
    // Memory usage threshold for warnings (in MB)
    memoryWarningThresholdMB: 1024,
    // CPU usage threshold for warnings (percentage)
    cpuWarningThreshold: 80,
  },

  // Site-specific configuration
  sites: {
    // Maximum pages to crawl per site (safety limit)
    maxPagesPerSite: parseInt(process.env.MAX_PAGES_PER_SITE) || 1000,
    // Request delay between requests (in ms)
    requestDelayMs: parseInt(process.env.REQUEST_DELAY_MS) || 1000,
    // User agent rotation
    rotateUserAgent: process.env.ROTATE_USER_AGENT === 'true',
  },

  // Error handling configuration
  errorHandling: {
    // Maximum consecutive errors before stopping worker
    maxConsecutiveErrors: 10,
    // Error reporting threshold
    errorReportingThreshold: 5,
    // Enable error recovery
    enableRecovery: true,
  },
};

/**
 * Get worker count from environment or default
 * @returns {number} Number of workers to spawn
 */
export function getWorkerCount() {
  const envWorkerCount = process.env.WORKER_COUNT;
  if (envWorkerCount) {
    const count = parseInt(envWorkerCount);
    if (isNaN(count) || count < 1) {
      console.warn(`Invalid WORKER_COUNT: ${envWorkerCount}, using default`);
      return config.workers.defaultCount;
    }
    return Math.min(count, 8); // Max 8 workers for safety
  }
  return config.workers.defaultCount;
}

/**
 * Get storage directory for worker
 * @param {string|number} workerId Worker ID
 * @returns {string} Storage directory path
 */
export function getWorkerStorageDir(workerId) {
  return config.storage.workerDirPattern.replace('{workerId}', workerId);
}

/**
 * Validate configuration
 * @returns {boolean} True if configuration is valid
 */
export function validateConfig() {
  const errors = [];

  if (config.workers.defaultCount < 1) {
    errors.push('Worker count must be at least 1');
  }

  if (config.workers.maxConcurrencyPerWorker < 1) {
    errors.push('Max concurrency per worker must be at least 1');
  }

  if (config.queue.retries < 0) {
    errors.push('Queue retries must be non-negative');
  }

  if (errors.length > 0) {
    console.error('Configuration validation errors:', errors);
    return false;
  }

  return true;
}

/**
 * Log current configuration
 */
export function logConfig() {
  console.log('🔧 Crawler Configuration:');
  console.log(`  Workers: ${getWorkerCount()}`);
  console.log(`  Max Concurrency per Worker: ${config.workers.maxConcurrencyPerWorker}`);
  console.log(`  Queue Name: ${config.queue.name}`);
  console.log(`  Log Level: ${config.logging.level}`);
  console.log(`  Headless: ${config.crawler.headless}`);
  console.log(`  Storage: ${config.storage.sharedDir}`);
}
