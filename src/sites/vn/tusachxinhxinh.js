export const tusachxinhxinh = {
  name: "tusachxinhxinh",
  locale: "vi",
  enabled: false,
  baseUrl: "https://tusachxinhxinh11.info",
  startPath: "/",
  maxPages: 5,
  selectors: {
    listManga: {
      container: ".comic-list",
      mangaLink: ".comic-list .comic-img a",
      nextPage: ".pager .next a",
      lastPage: "",
    },
    detailManga: {
      title: ".info-title",
      cover: ".img-thumbnail",
      description: ".follow-container + p",
      authors: "strong:contains(Tác giả:) + span",
      genres: ".comic-intro-text + .tags a",
      status: ".comic-stt",
      chapterLink: "td a.text-capitalize",
    },
    detailChapter: {
      chapterTitle: ".info-title.single-title",
      pages: "#view-chapter img",
      waitForSelector: "#view-chapter img",
    },
  },
};
