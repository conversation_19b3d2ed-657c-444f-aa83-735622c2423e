import * as extractors from "../extractors/index.js";

export async function handleChapterDetail({
  parseWithCheerio,
  log,
  request,
  waitForSelector,
}) {
  const { site } = request?.userData;
  log.info(`🔎 Scraping chapter link ${request.url}`);
  const $ = await parseWithCheerio();
  const chapterDetailSelectors = site?.selectors?.detailChapter;
  await waitForSelector(chapterDetailSelectors?.waitForSelector);

  const chapterDetail = extractors.chapterDetail(
    $,
    site?.selectors?.detailChapter,
    site,
  );
  log.info(`🇻🇳 Scraped Chapter ${chapterDetail?.title}`);
  log.info(`🌼 Scraped Pages ${chapterDetail?.pages.length} pages`);
}
